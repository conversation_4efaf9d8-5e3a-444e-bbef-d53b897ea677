# Story 3.2: 筛查策略设计器

## Status

Approved

## Story

**As a** 政策制定者，
**I want** 设计灵活的筛查策略，包含多种工具组合，
**so that** 评估不同筛查方案的有效性。

## Acceptance Criteria

1. 实现筛查策略配置窗口，支持开始/结束年龄设置
2. 配置筛查间隔和实施周期的灵活设置
3. 支持顺序筛查工具实施（后续工具不早于前一工具结束）
4. 实现筛查策略模板保存和加载功能
5. 添加策略配置验证，防止逻辑冲突
6. 创建策略比较和可视化功能

## Acceptance Criteria

1. 实现筛查策略配置窗口，支持开始/结束年龄设置
2. 配置筛查间隔和实施周期的灵活设置
3. 支持顺序筛查工具实施（后续工具不早于前一工具结束）
4. 实现筛查策略模板保存和加载功能
5. 添加策略配置验证，防止逻辑冲突
6. 创建策略比较和可视化功能

## Tasks / Subtasks

- [ ] 任务1：实现筛查策略核心数据结构 (AC: 1, 2)

  - [ ] 创建src/modules/screening/strategy.py文件
  - [ ] 实现ScreeningStrategy类，包含策略基本属性
  - [ ] 添加年龄范围配置（开始年龄、结束年龄）
  - [ ] 实现筛查间隔配置（年度、双年度、自定义）
  - [ ] 创建筛查工具序列配置
  - [ ] 添加策略元数据（名称、描述、创建日期）
- [ ] 任务2：实现顺序筛查工具管理 (AC: 3)

  - [ ] 创建src/modules/screening/tool_sequence.py文件
  - [ ] 实现ToolSequence类，管理工具执行顺序
  - [ ] 添加工具间时间约束验证
  - [ ] 实现条件性工具触发（基于前一工具结果）
  - [ ] 创建工具序列执行调度器
  - [ ] 添加序列执行状态跟踪
- [ ] 任务3：创建策略配置界面组件 (AC: 1, 2, 3)

  - [ ] 创建src/interfaces/desktop/widgets/strategy_designer.py
  - [ ] 实现StrategyDesignerWidget策略设计界面
  - [ ] 添加年龄范围选择控件（滑块、数字输入）
  - [ ] 创建筛查间隔配置面板
  - [ ] 实现工具序列拖拽配置界面
  - [ ] 添加策略预览和摘要显示
- [ ] 任务4：实现策略模板管理系统 (AC: 4)

  - [ ] 创建data/screening_strategies/目录结构
  - [ ] 设计策略模板文件格式（YAML）
  - [ ] 实现策略模板保存和加载功能
  - [ ] 添加预定义策略模板（常见筛查方案）
  - [ ] 创建策略模板验证和迁移功能
  - [ ] 实现策略模板分类和标签系统
- [ ] 任务5：添加策略配置验证系统 (AC: 5)

  - [ ] 扩展src/utils/validators.py，添加策略验证
  - [ ] 实现年龄范围逻辑验证
  - [ ] 添加筛查间隔合理性检查
  - [ ] 创建工具序列一致性验证
  - [ ] 实现策略成本效益预估验证
  - [ ] 添加策略冲突检测和警告
- [ ] 任务6：创建策略比较和可视化功能 (AC: 6)

  - [ ] 创建src/interfaces/desktop/widgets/strategy_comparison.py
  - [ ] 实现StrategyComparisonWidget比较界面
  - [ ] 添加策略参数对比表格
  - [ ] 创建策略时间线可视化
  - [ ] 实现策略成本效益比较图表
  - [ ] 添加策略导出和报告功能

## Dev Notes

### 筛查策略数据结构

```python
from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime

@dataclass
class ScreeningInterval:
    frequency_years: float          # 筛查频率（年）
    start_age: int                 # 开始年龄
    end_age: int                   # 结束年龄
    tool_type: ScreeningToolType   # 筛查工具类型

@dataclass
class ScreeningStrategy:
    name: str
    description: str
    intervals: List[ScreeningInterval]
    target_population: Dict        # 目标人群特征
    created_date: datetime
    version: str = "1.0"
  
    def validate(self) -> List[str]:
        """验证策略配置，返回错误列表"""
        errors = []
      
        # 年龄范围验证
        for interval in self.intervals:
            if interval.start_age >= interval.end_age:
                errors.append(f"开始年龄不能大于等于结束年龄: {interval}")
      
        # 间隔重叠检查
        for i, interval1 in enumerate(self.intervals):
            for j, interval2 in enumerate(self.intervals[i+1:], i+1):
                if self._intervals_overlap(interval1, interval2):
                    errors.append(f"筛查间隔重叠: {interval1} 和 {interval2}")
      
        return errors
```

### 策略模板文件格式

```yaml
# data/screening_strategies/china_national_guideline.yaml
screening_strategy:
  name: "中国结直肠癌筛查指南推荐方案"
  description: "基于中国结直肠癌筛查指南的标准筛查策略"
  version: "2023.1"
  
  target_population:
    age_range: [50, 75]
    risk_level: "average"
    exclusions: ["previous_colorectal_cancer", "inflammatory_bowel_disease"]
  
  intervals:
    - tool_type: "FIT"
      frequency_years: 1.0
      start_age: 50
      end_age: 75
      conditions:
        - type: "negative_result"
          action: "continue"
        - type: "positive_result"
          action: "trigger_colonoscopy"
  
    - tool_type: "COLONOSCOPY"
      frequency_years: 10.0
      start_age: 50
      end_age: 75
      trigger_conditions: ["fit_positive", "high_risk_factors"]
  
  cost_parameters:
    budget_constraint: 1000000  # 预算约束（元）
    cost_effectiveness_threshold: 50000  # 成本效益阈值
```

### 工具序列管理

```python
class ToolSequence:
    def __init__(self, strategy: ScreeningStrategy):
        self.strategy = strategy
        self.execution_queue = []
        self.completed_tools = []
  
    def schedule_next_screening(self, individual: Individual, current_time: float):
        """为个体安排下一次筛查"""
        eligible_intervals = self._get_eligible_intervals(individual, current_time)
      
        for interval in eligible_intervals:
            next_screening_time = self._calculate_next_screening_time(
                individual, interval, current_time
            )
          
            if next_screening_time:
                self.execution_queue.append(
                    ScheduledScreening(
                        individual_id=individual.id,
                        tool_type=interval.tool_type,
                        scheduled_time=next_screening_time,
                        interval_config=interval
                    )
                )
  
    def execute_screening(self, scheduled_screening: ScheduledScreening) -> ScreeningResult:
        """执行计划的筛查"""
        tool = ScreeningToolFactory.create_tool(scheduled_screening.tool_type)
        result = tool.perform_screening(scheduled_screening.individual)
      
        # 记录完成的筛查
        self.completed_tools.append(result)
      
        # 触发后续行动
        if result.is_positive:
            self._trigger_followup_actions(result)
      
        return result
```

### 策略配置界面设计

```python
class StrategyDesignerWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.strategy = ScreeningStrategy()
        self.setup_ui()
  
    def setup_ui(self):
        layout = QVBoxLayout()
      
        # 基本信息面板
        info_panel = self.create_info_panel()
        layout.addWidget(info_panel)
      
        # 年龄范围配置
        age_panel = self.create_age_range_panel()
        layout.addWidget(age_panel)
      
        # 筛查间隔配置
        interval_panel = self.create_interval_panel()
        layout.addWidget(interval_panel)
      
        # 工具序列配置
        sequence_panel = self.create_tool_sequence_panel()
        layout.addWidget(sequence_panel)
      
        # 预览和验证面板
        preview_panel = self.create_preview_panel()
        layout.addWidget(preview_panel)
      
        self.setLayout(layout)
```

### 策略验证规则

- **年龄范围**: 18 ≤ 开始年龄 < 结束年龄 ≤ 100
- **筛查间隔**: 0.5 ≤ 频率 ≤ 20年
- **工具序列**: 后续工具开始时间 ≥ 前一工具结束时间
- **成本约束**: 策略总成本 ≤ 预算限制
- **逻辑一致性**: 无冲突的筛查间隔和条件

### 预定义策略模板

- **中国国家指南**: FIT年度筛查 + 阳性结肠镜
- **美国USPSTF**: 多种工具选择，10年结肠镜或年度FIT
- **欧洲指南**: 双年度FIT + 风险分层
- **高风险人群**: 缩短间隔的强化筛查
- **资源受限**: 成本优化的基础筛查方案

### Testing

#### 测试文件位置

- `tests/unit/test_screening_strategy.py`
- `tests/unit/test_tool_sequence.py`
- `tests/unit/test_strategy_designer.py`
- `tests/integration/test_strategy_execution.py`

#### 测试标准

- 策略创建和配置测试
- 工具序列调度和执行测试
- 策略验证逻辑测试
- 模板保存和加载测试
- 界面交互和用户体验测试

#### 测试框架和模式

- 使用pytest-qt测试GUI组件
- Mock筛查工具测试策略执行
- 参数化测试验证不同策略配置
- 集成测试验证完整策略流程

#### 特定测试要求

- 策略验证准确性: 100%错误检测
- 界面响应时间: 所有操作 < 500ms
- 模板加载时间: < 100ms
- 策略执行一致性: 重复执行结果相同

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
